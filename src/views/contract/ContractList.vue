<script setup lang="ts">
import { ref, onMounted, computed, watch } from "vue";
import { useRoute } from "vue-router";
import {
  getContracts,
  createContract,
  updateContract,
  getCustomersSimpleList,
  ContractsParams,
} from "../../services/contract";
import type { ContractItem, ContractFormData } from "../../types/contract";
import { useToast } from "primevue/usetoast";
import { getSales } from "../../services/customer";
import {
  contractTypeOptions,
  contractBusinessOptions,
  contractStateOptions,
  contractTermOptions,
} from "../../utils/const";
import { initNumber, isoFormatDate } from "../../utils/common";
import { optionLoaders } from "../../utils/options";
import ContractDetail from "./ContractDetail.vue";

const contracts = ref<ContractItem[]>();
const loading = ref(false);
const totalRecords = ref(0);
const toast = useToast();
const route = useRoute();

const hasOperationPermission = computed(() => {
  const currentRoute = route.matched[route.matched.length - 1];
  return currentRoute?.meta?.operation ?? true;
});

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 筛选选项
const filterColumnOptions = [
  { label: "--", value: "--" },
  { label: "合同编码", value: "contract_num" },
  { label: "合同标题", value: "contract_title" },
  { label: "创建者", value: "create_user" },
];
const selectedFilterColumn = ref("--");
const filterValue = ref("");
const filterCustomerNum = ref("");

// Drawer 相关
const contractDrawerVisible = ref(false);
const editingContract = ref<ContractItem | null>(null);

// 表单验证错误状态
const fieldErrors = ref<Record<string, string>>({});
const isSubmitting = ref(false);

// Dialog 相关
const contractDetailVisible = ref(false);
const viewingContract = ref<ContractItem | null>(null);
const contractForm = ref<ContractFormData>({
  contract_num: "",
  contract_title: "",
  business: "",
  alias_customer_num: "",
  contract_type: "",
  sale_name: "",
  frame_contract_num: "",
  frame_legal_num: "",
  frame_oa_num: "",
  main_customer_num: "",
  sign_contract_entity: "",
  contract_legal_num: "",
  contract_oa_num: "",
  other_party_num: "",
  upload_file: "",
  remark: "",
  contract_start_date: new Date(),
  contract_effect_months: 0,
  is_auto_delay: "",
  auto_delay_num: 0,
  contract_month_amount: 0.0,
  contract_summary: "",
  contract_term: 0,
  contract_object: "",
  contract_end_date: new Date(),
  append_contract_way: "",
  append_contract_explain: "",
  state: "",
});

// 加载客户简单列表
const customerOptions = ref<{ label: string; value: string }[]>([]);
const loadCustomerOptions = async () => {
  try {
    const response = await getCustomersSimpleList();
    customerOptions.value = response.data.map((item) => ({
      label: `${item.customer_name} (${item.customer_num})`,
      value: item.customer_num,
    }));
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载客户列表失败",
      life: 3000,
    });
  }
};

// 加载销售列表
const salesOptions = ref<{ label: string; value: string }[]>([]);
const loadSales = async () => {
  try {
    const response = await getSales();
    salesOptions.value = response.map((item) => ({
      label: item.sale_name,
      value: item.sale_name,
    }));
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "Error",
      detail: "加载销售列表失败",
      life: 3000,
    });
  }
};

// 签约主体选项
const signContractEntityOptions = ref<{ label: string; value: string }[]>([]);
const loadSignContractEntityOptions = () =>
  optionLoaders.signContractEntity(signContractEntityOptions);

// 加载合同列表数据
const loadContracts = async () => {
  try {
    loading.value = true;
    const params: ContractsParams = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };
    if (filterValue.value && selectedFilterColumn.value) {
      params.filter = filterValue.value;
      params.filterColumn = selectedFilterColumn.value;
    }
    if (filterCustomerNum.value) {
      params.main_customer_num = filterCustomerNum.value;
    }

    // 过滤空值
    Object.keys(params).forEach((key) => {
      if (params[key] === "") {
        delete params[key];
      }
    });

    const response = await getContracts(params);
    contracts.value = response.data.records;
    totalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载合同列表失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadContracts();
};

// 搜索
const handleSearch = () => {
  lazyParams.value.page = 1;
  loadContracts();
};

// 表单验证函数
const validateForm = (): boolean => {
  fieldErrors.value = {};
  let isValid = true;

  // 必填字段验证
  const requiredFields = [
    { key: "contract_title", label: "合同标题" },
    { key: "main_customer_num", label: "客户编码" },
    { key: "business", label: "业务类型" },
    { key: "contract_type", label: "合同类型" },
    { key: "sale_name", label: "销售" },
    { key: "state", label: "状态" },
    { key: "sign_contract_entity", label: "签约主体" },
    { key: "contract_term", label: "合同期限" },
    { key: "contract_start_date", label: "合同开始日期" },
    { key: "is_auto_delay", label: "是否自动延期" },
  ];

  requiredFields.forEach((field) => {
    const value = contractForm.value[field.key as keyof ContractFormData];
    if (
      value === null ||
      value === undefined ||
      (typeof value === "string" && value.trim() === "") ||
      (typeof value === "number" && isNaN(value))
    ) {
      fieldErrors.value[field.key] = `${field.label}不能为空`;
      isValid = false;
    }
  });

  // 合同生效月数验证
  if (contractForm.value.contract_effect_months <= 0) {
    fieldErrors.value.contract_effect_months = "合同生效月数必须大于0";
    isValid = false;
  }

  // 条件必填字段验证：当自动延期时
  if (contractForm.value.is_auto_delay === 'Y') {
    // 自动延期次数必填
    if (!contractForm.value.auto_delay_num || contractForm.value.auto_delay_num <= 0) {
      fieldErrors.value.auto_delay_num = "自动延期次数不能为空且必须大于0";
      isValid = false;
    }
  }

  // 条件必填字段验证：当为固定期限合同时
  if (contractForm.value.contract_term === 0) {
    // 合同结束日期必填
    if (!contractForm.value.contract_end_date) {
      fieldErrors.value.contract_end_date = "固定期限合同的结束日期不能为空";
      isValid = false;
    }
  }

  return isValid;
};

// 清空表单错误
const clearFieldErrors = () => {
  fieldErrors.value = {};
};

// 打开新增合同 Drawer
const openNew = async () => {
  editingContract.value = null;
  const contractNum = await initNumber("SRHT");
  contractForm.value = {
    contract_num: contractNum,
    contract_title: "",
    business: "",
    alias_customer_num: "",
    contract_type: "",
    sale_name: "",
    frame_contract_num: "",
    frame_legal_num: "",
    frame_oa_num: "",
    main_customer_num: "",
    sign_contract_entity: "",
    contract_legal_num: "",
    contract_oa_num: "",
    other_party_num: "",
    upload_file: "",
    remark: "",
    contract_start_date: new Date(),
    contract_effect_months: 0,
    is_auto_delay: "",
    auto_delay_num: 0,
    contract_month_amount: 0.0,
    contract_summary: "",
    contract_term: 0,
    contract_object: "",
    contract_end_date: new Date(),
    append_contract_way: "",
    append_contract_explain: "",
    state: "",
  };

  // 清空表单错误
  clearFieldErrors();
  contractDrawerVisible.value = true;
  loadSales();
};

// 查看合同详情
const viewContract = (contract: ContractItem) => {
  viewingContract.value = contract;
  contractDetailVisible.value = true;
};

// 打开编辑合同 Drawer
const editContract = (contract: ContractItem) => {
  if (!hasOperationPermission.value) return;
  editingContract.value = contract;
  contractForm.value = { ...contract };

  // 清空表单错误
  clearFieldErrors();
  contractDrawerVisible.value = true;
  loadSales();
};

const handleDateFields = async (contractFormValue: ContractFormData) => {
  // 将iso格式的日期转换为字符串
  let dateFields = ["contract_start_date", "contract_end_date"];
  dateFields.forEach((field) => {
    (contractFormValue as any)[field] = contractFormValue[
      field as keyof typeof contractFormValue
    ]
      ? isoFormatDate(
          new Date(
            contractFormValue[field as keyof typeof contractFormValue] as
              | string
              | number
              | Date
          )
        )
      : null;
  });
};

// 保存合同信息
const saveContract = async () => {
  // 前端表单验证
  if (!validateForm()) {
    toast.add({
      severity: "error",
      summary: "表单验证失败",
      detail: "请检查必填字段",
      life: 3000,
    });
    return;
  }

  isSubmitting.value = true;
  await handleDateFields(contractForm.value);

  try {
    if (editingContract.value) {
      await updateContract(
        editingContract.value.id.toString(),
        contractForm.value
      );
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "合同更新成功",
        life: 3000,
      });
    } else {
      await createContract(contractForm.value);
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "合同创建成功",
        life: 3000,
      });
    }
    contractDrawerVisible.value = false;
    clearFieldErrors();
    loadContracts();
  } catch (error: any) {
    // 处理422验证错误
    if (error.response?.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      // 清空旧错误
      fieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        fieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
      toast.add({
        severity: "error",
        summary: "字段校验失败",
        detail: Object.values(fieldErrors.value).join("; "),
        life: 4000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: "保存合同失败",
        life: 3000,
      });
    }
  } finally {
    isSubmitting.value = false;
  }
};

// 监听合同期限变化
watch(
  () => contractForm.value.contract_term,
  (newValue) => {
    // 当选择无固定期限合同时，清空合同结束日期
    if (newValue === 1) {
      contractForm.value.contract_end_date = null as any;
    }
  }
);

// 监听是否自动延期变化
watch(
  () => contractForm.value.is_auto_delay,
  (newValue) => {
    // 当选择不自动延期时，清空自动延期次数
    if (newValue === 'N') {
      contractForm.value.auto_delay_num = 0;
    }
  }
);

// 页面加载时获取数据
onMounted(() => {
  loadCustomerOptions();
  loadContracts();
  loadSignContractEntityOptions();
});
</script>

<template>
  <div class="contract-list-container">
    <Toast />
    <div class="card">
      <div class="card-header">
        <Message variant="simple" size="large">合同信息</Message>
        <Button
          label="新建合同"
          icon="pi pi-plus"
          @click="openNew"
          :disabled="!hasOperationPermission"
        />
      </div>

      <!-- 筛选区域 -->
      <Toolbar class="mb-2">
        <template #start>
          <Select
            id="main_customer_num"
            v-model="filterCustomerNum"
            :options="customerOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="请选择客户"
            :showClear="true"
            filter
            @change="loadContracts"
          />
        </template>
        <template #end>
          <FloatLabel class="mr-2">
            <Select
              v-model="selectedFilterColumn"
              :options="filterColumnOptions"
              optionLabel="label"
              optionValue="value"
              placeholder="请选择筛选字段"
              style="width: 15rem"
              size="normal"
            />
          </FloatLabel>
          <FloatLabel class="mr-2">
            <InputText v-model="filterValue" />
            <label>搜索值</label>
          </FloatLabel>
          <Button label="搜索" icon="pi pi-search" @click="handleSearch" />
        </template>
      </Toolbar>

      <!-- 数据表格 -->
      <DataTable
        :value="contracts"
        :lazy="true"
        :paginator="true"
        :rows="20"
        :rowsPerPageOptions="[10, 20, 50]"
        :totalRecords="totalRecords"
        :loading="loading"
        @page="onPage($event)"
        showGridlines
        stripedRows
        scrollable
        scrollHeight="calc(100vh - 25rem)"
      >
        <template #empty>
          <div class="empty-message">
            <i
              class="pi pi-inbox"
              style="
                font-size: 2rem;
                color: var(--p-text-color-secondary);
                margin-bottom: 1rem;
              "
            ></i>
            <p>暂无合同数据</p>
          </div>
        </template>
        <Column
          field="contract_num"
          header="合同编码"
          style="min-width: 15rem"
        />
        <Column
          field="contract_title"
          header="合同标题"
          style="min-width: 30rem"
        />
        <Column field="sale_name" header="销售" style="min-width: 10rem" />
        <Column
          field="contract_object"
          header="合同标的"
          style="min-width: 10rem"
        />
        <Column
          field="main_customer_num"
          header="客户编码"
          style="min-width: 10rem"
        />
        <Column field="business" header="业务类型" style="min-width: 10rem" />
        <Column
          field="contract_type"
          header="合同类型"
          style="min-width: 10rem"
        />
        <Column field="state" header="状态" style="min-width: 10rem">
          <template #body="slotProps">
            <Tag severity="info" :value="slotProps.data.state" />
          </template>
        </Column>
        <Column
          field="contract_start_date"
          header="合同开始日期"
          style="min-width: 10rem"
        />
        <Column field="create_user" header="创建者" style="min-width: 10rem" />
        <Column
          field="group_approve_state"
          header="集团审批结果"
          style="min-width: 10rem"
        />
        <Column
          header="操作"
          :exportable="false"
          style="min-width: 12rem"
          alignFrozen="right"
          frozen
        >
          <template #body="slotProps">
            <div class="flex gap-2">
              <Button
                icon="pi pi-eye"
                outlined
                rounded
                severity="info"
                @click="viewContract(slotProps.data)"
                v-tooltip.top="'查看详情'"
              />
              <Button
                icon="pi pi-pencil"
                outlined
                rounded
                severity="success"
                :disabled="!hasOperationPermission"
                @click="editContract(slotProps.data)"
                v-tooltip.top="'编辑合同'"
              />
            </div>
          </template>
        </Column>
      </DataTable>
    </div>

    <!-- 新建/编辑合同 Drawer -->
    <Drawer
      v-model:visible="contractDrawerVisible"
      position="right"
      :style="{ width: '80rem' }"
      :modal="true"
      :closable="true"
      :dismissable="true"
      :showCloseIcon="true"
      :header="editingContract ? '编辑合同信息' : '新建合同信息'"
      class="p-fluid contract-drawer"
    >
      <div class="p-4">
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-header">
            <h3 class="section-title">基本信息</h3>
            <Divider />
          </div>
          <div class="section-content">
            <Fluid>
              <div class="grid grid-cols-3 gap-4">
                <div class="field">
                  <label for="contract_num" class="required">合同编码</label>
                  <InputText
                    v-model="contractForm.contract_num"
                    class="field-input"
                    :disabled="true"
                  />
                </div>
                <div class="field">
                  <label for="contract_title" class="required">合同标题</label>
                  <InputText
                    v-model="contractForm.contract_title"
                    :class="{ 'p-invalid': fieldErrors.contract_title }"
                  />
                  <small v-if="fieldErrors.contract_title" class="p-error">
                    {{ fieldErrors.contract_title }}
                  </small>
                </div>
                <div class="field">
                  <label for="contract_object">合同标的</label>
                  <InputText v-model="contractForm.contract_object" />
                </div>
                <div class="field">
                  <label for="main_customer_num" class="required"
                    >客户编码</label
                  >
                  <Select
                    v-model="contractForm.main_customer_num"
                    :options="customerOptions"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="请选择客户"
                    filter
                    :class="{ 'p-invalid': fieldErrors.main_customer_num }"
                  />
                  <small v-if="fieldErrors.main_customer_num" class="p-error">
                    {{ fieldErrors.main_customer_num }}
                  </small>
                </div>
                <div class="field">
                  <label for="business" class="required">业务类型</label>
                  <Select
                    v-model="contractForm.business"
                    :options="contractBusinessOptions"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="选择业务类型"
                    :class="{ 'p-invalid': fieldErrors.business }"
                  />
                  <small v-if="fieldErrors.business" class="p-error">
                    {{ fieldErrors.business }}
                  </small>
                </div>
                <div class="field">
                  <label for="contract_type" class="required">合同类型</label>
                  <Select
                    v-model="contractForm.contract_type"
                    :options="contractTypeOptions"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="选择归属类型"
                    :class="{ 'p-invalid': fieldErrors.contract_type }"
                  />
                  <small v-if="fieldErrors.contract_type" class="p-error">
                    {{ fieldErrors.contract_type }}
                  </small>
                </div>
                <div class="field">
                  <label for="sale_name" class="required">销售</label>
                  <Select
                    v-model="contractForm.sale_name"
                    :options="salesOptions"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="选择销售"
                    filter
                    :class="{ 'p-invalid': fieldErrors.sale_name }"
                  />
                  <small v-if="fieldErrors.sale_name" class="p-error">
                    {{ fieldErrors.sale_name }}
                  </small>
                </div>
                <div class="field">
                  <label for="state" class="required">状态</label>
                  <Select
                    v-model="contractForm.state"
                    :options="contractStateOptions"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="选择状态"
                    :class="{ 'p-invalid': fieldErrors.state }"
                  />
                  <small v-if="fieldErrors.state" class="p-error">
                    {{ fieldErrors.state }}
                  </small>
                </div>
                <div class="field">
                  <label for="sign_contract_entity" class="required"
                    >签约主体</label
                  >
                  <Select
                    v-model="contractForm.sign_contract_entity"
                    :options="signContractEntityOptions"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="选择签约主体"
                    :class="{ 'p-invalid': fieldErrors.sign_contract_entity }"
                  />
                  <small
                    v-if="fieldErrors.sign_contract_entity"
                    class="p-error"
                  >
                    {{ fieldErrors.sign_contract_entity }}
                  </small>
                </div>
              </div>
            </Fluid>
          </div>
        </div>

        <!-- 合同条款 -->
        <div class="form-section">
          <div class="section-header">
            <h3 class="section-title">合同条款</h3>
            <Divider />
          </div>
          <div class="section-content">
            <Fluid>
              <div class="grid grid-cols-3 gap-4">
                <div class="field">
                  <label for="contract_term" class="required">合同期限</label>
                  <Select
                    v-model="contractForm.contract_term"
                    :options="contractTermOptions"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="选择合同期限"
                    :class="{ 'p-invalid': fieldErrors.contract_term }"
                  />
                  <small v-if="fieldErrors.contract_term" class="p-error">
                    {{ fieldErrors.contract_term }}
                  </small>
                </div>
                <div class="field">
                  <label for="contract_effect_months">合同生效月数</label>
                  <InputNumber
                    v-model="contractForm.contract_effect_months"
                    showButtons
                    :min="1"
                  />
                </div>
                <div class="field">
                  <label for="contract_start_date" class="required"
                    >合同开始日期</label
                  >
                  <DatePicker
                    v-model="contractForm.contract_start_date"
                    dateFormat="yy-mm-dd"
                    showIcon
                    :showTime="false"
                    :manualInput="false"
                    :class="{ 'p-invalid': fieldErrors.contract_start_date }"
                  />
                  <small v-if="fieldErrors.contract_start_date" class="p-error">
                    {{ fieldErrors.contract_start_date }}
                  </small>
                </div>
                <div class="field">
                  <label for="contract_end_date" :class="{ 'required': contractForm.contract_term === 0 }">合同结束日期</label>
                  <DatePicker
                    v-model="contractForm.contract_end_date"
                    dateFormat="yy-mm-dd"
                    showIcon
                    :showTime="false"
                    :manualInput="false"
                    :disabled="contractForm.contract_term === 1"
                    :placeholder="
                      contractForm.contract_term === 1
                        ? '无固定期限合同无需设置结束日期'
                        : '选择合同结束日期'
                    "
                    :class="{ 'p-invalid': fieldErrors.contract_end_date }"
                  />
                  <small v-if="fieldErrors.contract_end_date" class="p-error">
                    {{ fieldErrors.contract_end_date }}
                  </small>
                </div>
                <div class="field">
                  <label for="contract_month_amount">合同月度金额</label>
                  <InputNumber
                    v-model="contractForm.contract_month_amount"
                    mode="currency"
                    currency="CNY"
                    :minFractionDigits="2"
                  />
                </div>
                <div class="field">
                  <label for="is_auto_delay" class="required"
                    >是否自动延期</label
                  >
                  <Select
                    v-model="contractForm.is_auto_delay"
                    :options="[
                      { label: '是', value: 'Y' },
                      { label: '否', value: 'N' },
                    ]"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="选择是否自动延期"
                    :class="{ 'p-invalid': fieldErrors.is_auto_delay }"
                  />
                  <small v-if="fieldErrors.is_auto_delay" class="p-error">
                    {{ fieldErrors.is_auto_delay }}
                  </small>
                </div>
                <div class="field">
                  <label for="auto_delay_num" :class="{ 'required': contractForm.is_auto_delay === 'Y' }">自动延期次数</label>
                  <InputNumber
                    v-model="contractForm.auto_delay_num"
                    showButtons
                    :min="1"
                    :disabled="contractForm.is_auto_delay === 'N'"
                    :placeholder="contractForm.is_auto_delay === 'N' ? '不自动延期无需设置次数' : '输入延期次数'"
                    :class="{ 'p-invalid': fieldErrors.auto_delay_num }"
                  />
                  <small v-if="fieldErrors.auto_delay_num" class="p-error">
                    {{ fieldErrors.auto_delay_num }}
                  </small>
                </div>
              </div>
            </Fluid>
          </div>
        </div>

        <!-- 编码信息 -->
        <div class="form-section">
          <div class="section-header">
            <h3 class="section-title">编码信息</h3>
            <Divider />
          </div>
          <div class="section-content">
            <Fluid>
              <div class="grid grid-cols-3 gap-4">
                <div class="field">
                  <label for="alias_customer_num">别名客户编码</label>
                  <InputText v-model="contractForm.alias_customer_num" />
                </div>
                <div class="field">
                  <label for="other_party_num">合同对方编号</label>
                  <InputText v-model="contractForm.other_party_num" />
                </div>
                <div class="field">
                  <label for="frame_contract_num">框架合同编码</label>
                  <InputText v-model="contractForm.frame_contract_num" />
                </div>
                <div class="field">
                  <label for="contract_legal_num">合同法务编码</label>
                  <InputText v-model="contractForm.contract_legal_num" />
                </div>
                <div class="field">
                  <label for="frame_legal_num">框架法务编码</label>
                  <InputText v-model="contractForm.frame_legal_num" />
                </div>
                <div class="field">
                  <label for="contract_oa_num">合同OA编码</label>
                  <InputText v-model="contractForm.contract_oa_num" />
                </div>
                <div class="field">
                  <label for="frame_oa_num">框架OA编码</label>
                  <InputText v-model="contractForm.frame_oa_num" />
                </div>
                <div class="field">
                  <label for="upload_file">上传文件</label>
                  <InputText v-model="contractForm.upload_file" />
                </div>
              </div>
            </Fluid>
          </div>
        </div>

        <!-- 补充信息 -->
        <div class="form-section">
          <div class="section-header">
            <h3 class="section-title">补充信息</h3>
            <Divider />
          </div>
          <div class="section-content">
            <Fluid>
              <div class="grid grid-cols-2 gap-4">
                <div class="field">
                  <label for="contract_summary">合同摘要</label>
                  <Textarea
                    v-model="contractForm.contract_summary"
                    rows="3"
                    style="resize: none"
                  />
                </div>
                <div class="field">
                  <label for="append_contract_way">补充合同方式</label>
                  <InputText v-model="contractForm.append_contract_way" />
                </div>
                <div class="field">
                  <label for="append_contract_explain">补充合同说明</label>
                  <Textarea
                    v-model="contractForm.append_contract_explain"
                    rows="3"
                    style="resize: none"
                  />
                </div>
                <div class="field">
                  <label for="remark">备注</label>
                  <Textarea
                    v-model="contractForm.remark"
                    rows="3"
                    style="resize: none"
                  />
                </div>
              </div>
            </Fluid>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-2">
          <Button
            label="取消"
            icon="pi pi-times"
            @click="contractDrawerVisible = false"
            severity="secondary"
            outlined
          />
          <Button
            label="保存"
            icon="pi pi-check"
            @click="saveContract"
            severity="success"
            :loading="isSubmitting"
            :disabled="isSubmitting"
          />
        </div>
      </template>
    </Drawer>

    <!-- 查看合同详情 -->
    <ContractDetail
      v-model:visible="contractDetailVisible"
      :contract="viewingContract"
    />
  </div>
</template>

<style scoped>
.contract-list-container {
  padding: 1rem;
  height: calc(100vh - 10rem);
}

.card {
  background: var(--white);
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 1px 3px 0 rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.field {
  margin-bottom: 0.5rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--p-primary-color);
  letter-spacing: -0.025em;
}

.field label.required::after {
  content: " *";
  color: #ff3b30;
  font-weight: 600;
  margin-left: 2px;
}

/* 表单验证错误样式 */
.p-error {
  color: #ff3b30;
  font-size: 0.75rem;
  font-weight: 400;
  margin-top: 0.25rem;
  display: block;
  line-height: 1.2;
}

:deep(.p-invalid) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 1px #ff3b30 !important;
}

:deep(.p-invalid:focus) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 3px rgba(255, 59, 48, 0.1) !important;
}

/* 提交按钮加载状态 */
:deep(.p-button:disabled) {
  opacity: 0.6;
  cursor: not-allowed;
}

.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--surface-ground);
  border-radius: 6px;
}

.empty-message p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
}

:deep(.contract-drawer) {
  .p-drawer-content {
    display: flex;
    flex-direction: column;
  }
}

.form-section {
  margin-bottom: 2rem;
  background: var(--surface-card);
  border-radius: 12px;
  border: 1px solid var(--surface-border);
  overflow: hidden;
}

.section-header {
  padding: 1.5rem 1.5rem 0 1.5rem;
  background: var(--surface-card);
}

.section-title {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  letter-spacing: -0.025em;
}

.section-content {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.field-input:disabled {
  background-color: var(--p-surface-100);
  color: var(--p-text-color-secondary);
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }

  .grid-cols-3 {
    grid-template-columns: 1fr;
  }

  .section-header,
  .section-content {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
</style>
