<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { useToast } from "primevue/usetoast";
import {
  getOrderDetail,
  changeServiceStatus,
} from "../../services/income/order";
import type { OrderItem } from "../../types/order";
import { isoFormatDate, isoFormatDatetime } from "../../utils/common"


const props = defineProps<{
  orderId: number;
  confirm: any;
  dialogKey: string;
}>();

const router = useRouter();
const toast = useToast();
const loading = ref(false);
const orderDetail = ref<OrderItem | null>(null);
const activeStep = ref("I");
const selectedBranch = ref<"installation" | "termination" | "modification">(
  "installation"
);

// 表单数据
const formData = ref({
  order_remark: "",
  finished_remark: "",
  job_status: "",
  product_scheme: "",
  new_build_start_time: null as Date | null,
  remove_required_finished_date: null as Date | null,
  remove_build_start_time: null as Date | null,
});

// 派工状态选项
const job_statusOptions = [
  { label: "已派工", value: "已派工" },
  { label: "未派工", value: "未派工" },
  { label: "派工撤销", value: "派工撤销" },
];

// 定义状态类型
type InstallationStatus =
  | "新装暂存"
  | "新装下单审核"
  | "新装派单"
  | "新装实施"
  | "服务中";

type TerminationStatus =
  | "拆机回退"
  | "拆机下单审核"
  | "拆机派单"
  | "拆机实施"
  | "拆机维护信息复核"
  | "服务终止";

type ModificationStatus =
  | "变更拆机回退"
  | "变更拆机下单审核"
  | "变更拆机派单"
  | "变更拆机实施"
  | "变更拆机维护信息复核"
  | "变更服务终止";

type ServiceStatus =
  | InstallationStatus
  | TerminationStatus
  | ModificationStatus;

type InstallationStepValue = "I" | "II" | "III" | "IV" | "V";
type TerminationStepValue = "I" | "II" | "III" | "IV" | "V" | "VI";
type ModificationStepValue = "I" | "II" | "III" | "IV" | "V" | "VI";

type StepValue =
  | InstallationStepValue
  | TerminationStepValue
  | ModificationStepValue;

// 安装流程状态
const INSTALLATION_STATUSES = [
  { value: "I", label: "新装暂存" },
  { value: "II", label: "新装下单审核" },
  { value: "III", label: "新装派单" },
  { value: "IV", label: "新装实施" },
  { value: "V", label: "服务中" },
] as const;

// 服务终止流程状态
const TERMINATION_STATUSES = [
  { value: "I", label: "拆机回退" },
  { value: "II", label: "拆机下单审核" },
  { value: "III", label: "拆机派单" },
  { value: "IV", label: "拆机实施" },
  { value: "V", label: "拆机维护信息复核" },
  { value: "VI", label: "服务终止" },
] as const;

// 变更服务终止流程状态
const MODIFICATION_STATUSES = [
  { value: "I", label: "变更拆机回退" },
  { value: "II", label: "变更拆机下单审核" },
  { value: "III", label: "变更拆机派单" },
  { value: "IV", label: "变更拆机实施" },
  { value: "V", label: "变更拆机维护信息复核" },
  { value: "VI", label: "变更服务终止" },
] as const;

// 当前流程状态列表
const currentStatuses = computed(() => {
  switch (selectedBranch.value) {
    case "installation":
      return INSTALLATION_STATUSES;
    case "termination":
      return TERMINATION_STATUSES;
    case "modification":
      return MODIFICATION_STATUSES;
  }
});

// 状态值映射
const installationStatusValueMap: Record<
  InstallationStatus,
  InstallationStepValue
> = {
  新装暂存: "I",
  新装下单审核: "II",
  新装派单: "III",
  新装实施: "IV",
  服务中: "V",
};

const terminationStatusValueMap: Record<
  TerminationStatus,
  TerminationStepValue
> = {
  拆机回退: "I",
  拆机下单审核: "II",
  拆机派单: "III",
  拆机实施: "IV",
  拆机维护信息复核: "V",
  服务终止: "VI",
};

const modificationStatusValueMap: Record<
  ModificationStatus,
  ModificationStepValue
> = {
  变更拆机回退: "I",
  变更拆机下单审核: "II",
  变更拆机派单: "III",
  变更拆机实施: "IV",
  变更拆机维护信息复核: "V",
  变更服务终止: "VI",
};

const installationStatusLabelMap: Record<
  InstallationStepValue,
  InstallationStatus
> = {
  I: "新装暂存",
  II: "新装下单审核",
  III: "新装派单",
  IV: "新装实施",
  V: "服务中",
};

const terminationStatusLabelMap: Record<
  TerminationStepValue,
  TerminationStatus
> = {
  I: "拆机回退",
  II: "拆机下单审核",
  III: "拆机派单",
  IV: "拆机实施",
  V: "拆机维护信息复核",
  VI: "服务终止",
};

const modificationStatusLabelMap: Record<
  ModificationStepValue,
  ModificationStatus
> = {
  I: "变更拆机回退",
  II: "变更拆机下单审核",
  III: "变更拆机派单",
  IV: "变更拆机实施",
  V: "变更拆机维护信息复核",
  VI: "变更服务终止",
};

// 获取当前状态的标签
const getStatusLabel = (stepValue: StepValue): string => {
  if (selectedBranch.value === "termination") {
    return terminationStatusLabelMap[stepValue as TerminationStepValue];
  } else if (selectedBranch.value === "modification") {
    return modificationStatusLabelMap[stepValue as ModificationStepValue];
  } else {
    return installationStatusLabelMap[stepValue as InstallationStepValue];
  }
};

// 加载订单详情
const loadOrderDetail = async () => {
  try {
    loading.value = true;
    const orderId = Number(props.orderId);
    if (isNaN(orderId)) {
      throw new Error("Invalid order ID");
    }
    const response = await getOrderDetail(orderId);
    orderDetail.value = response.data;

    // 确定当前流程和状态
    const status = orderDetail.value.service_status as ServiceStatus;

    // 确定当前所处的流程分支
    if (status in installationStatusValueMap) {
      selectedBranch.value = "installation";
      activeStep.value =
        installationStatusValueMap[status as InstallationStatus];
    } else if (status in terminationStatusValueMap) {
      selectedBranch.value = "termination";
      activeStep.value = terminationStatusValueMap[status as TerminationStatus];
    } else if (status in modificationStatusValueMap) {
      selectedBranch.value = "modification";
      activeStep.value =
        modificationStatusValueMap[status as ModificationStatus];
    }

    // 初始化表单数据
    initializeFormData();
  } catch (error) {
    console.log(error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载订单详情失败",
      life: 3000,
    });
    router.push("/order-management/orders-info");
  } finally {
    loading.value = false;
  }
};

// 初始化表单数据
const initializeFormData = () => {
  if (!orderDetail.value) return;
  // 初始化订单备注
  if (orderDetail.value.order_remark) {
    formData.value.order_remark = orderDetail.value.order_remark;
  }
  // 初始化完工备注
  if (orderDetail.value.finished_remark) {
    formData.value.finished_remark = orderDetail.value.finished_remark;
  }
  // 初始化派工状态
  if (orderDetail.value.job_status) {
    formData.value.job_status = orderDetail.value.job_status;
  }
  // 初始化产品方案
  if (orderDetail.value.product_scheme) {
    formData.value.product_scheme = orderDetail.value.product_scheme;
  }
  // 初始化新装实施时间
  if (orderDetail.value.new_build_start_time) {
    formData.value.new_build_start_time = new Date(
      parseInt(orderDetail.value.new_build_start_time.toString()) * 1000
    );
  }
  // 初始化拆机要求完工日
  if (orderDetail.value.remove_required_finished_date) {
    formData.value.remove_required_finished_date = new Date(orderDetail.value.remove_required_finished_date);
  }
  // 初始化拆机实施时间
  if (orderDetail.value.remove_build_start_time) {
    formData.value.remove_build_start_time = new Date(orderDetail.value.remove_build_start_time);
  }
};

// 切换到终止服务流程
const goToTerminationProcess = (type: "termination" | "modification") => {
  if (!orderDetail.value || orderDetail.value.service_status !== "服务中") {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "只有在服务中状态才可以选择流程分支",
      life: 3000,
    });
    return;
  }

  props.confirm.require({
    group: props.dialogKey,
    header: `确认进入${
      type === "termination" ? "服务终止" : "变更服务终止"
    }流程`,
    icon: "pi pi-exclamation-triangle",
    message: `是否确认进入「${
      type === "termination" ? "服务终止" : "变更服务终止"
    }」流程？此操作不可逆。`,
    rejectProps: {
      label: "取消",
      severity: "secondary",
      outlined: true,
    },
    acceptProps: {
      label: "确认",
      severity: "success",
    },
    accept: async () => {
      try {
        // 首先更新服务状态到对应流程的第一步
        const orderId = Number(props.orderId);
        if (isNaN(orderId)) {
          throw new Error("Invalid order ID");
        }

        const newStatus = type === "termination" ? "拆机回退" : "变更拆机回退";
        await changeServiceStatus(orderId, {
          service_status: newStatus as ServiceStatus,
        });

        // 切换当前tab的流程分支
        selectedBranch.value = type;
        activeStep.value = "I";

        toast.add({
          severity: "success",
          summary: "成功",
          detail: `已切换到${
            type === "termination" ? "服务终止" : "变更服务终止"
          }流程`,
          life: 3000,
        });

        await loadOrderDetail();
      } catch (error) {
        toast.add({
          severity: "error",
          summary: "错误",
          detail: "切换流程失败",
          life: 3000,
        });
      }
    },
    reject: () => {},
  });
};

// 处理状态变更
const handleStatusChange = async (
  newStatus: StepValue,
  direction: "next" | "back"
) => {
  if (!orderDetail.value) return;

  props.confirm.require({
    group: props.dialogKey,
    header: "确认变更状态",
    icon: "pi pi-exclamation-triangle",
    message: `是否确认${
      direction === "next" ? "进入" : "返回"
    }「${getStatusLabel(newStatus)}」状态？`,
    rejectProps: {
      label: "取消",
      severity: "secondary",
      outlined: true,
    },
    acceptProps: {
      label: "确认",
      severity: "success",
    },
    accept: async () => {
      try {
        const orderId = Number(props.orderId);
        if (isNaN(orderId)) {
          throw new Error("Invalid order ID");
        }

        // 构建请求数据
        const postData: {
          service_status: ServiceStatus;
          finished_remark?: string;
          new_build_start_time?: string;
          order_remark?: string;
          job_status?: string;
          product_scheme?: string;
          remove_required_finished_date?: string;
          remove_build_start_time?: string;
        } = {
          service_status: getStatusLabel(newStatus) as ServiceStatus,
        };

        // 根据不同状态添加不同字段
        if (selectedBranch.value === "installation") {
          if (!handleInstallationStatus(newStatus, postData)) return;
        } else if (selectedBranch.value === "termination") {
          if (!handleTerminationStatus(newStatus, postData)) return;
        } else if (selectedBranch.value === "modification") {
          if (!handleModificationStatus(newStatus, postData)) return;
        }

        await changeServiceStatus(orderId, postData);
        toast.add({
          severity: "success",
          summary: "成功",
          detail: "状态更新成功",
          life: 3000,
        });
        activeStep.value = newStatus;
        await loadOrderDetail();
      } catch (error) {
        toast.add({
          severity: "error",
          summary: "错误",
          detail: "状态更新失败",
          life: 3000,
        });
      }
    },
    reject: () => {},
  });
};

// 处理安装流程状态
const handleInstallationStatus = (
  newStatus: StepValue,
  postData: any
): boolean => {
  switch (newStatus) {
    case "I": // 返回新装暂存
      // 返回暂存状态不需要额外字段
      break;
    case "II": // 从新装暂存到新装下单审核
      // 进入下单审核阶段不需要填写任何信息
      break;
    case "III": // 从新装下单审核到新装派单
      // 完成下单审核阶段，需要填写审核相关信息
      if (
        !formData.value.order_remark ||
        !formData.value.finished_remark
      ) {
        toast.add({
          severity: "error",
          summary: "错误",
          detail: "请填写订单备注和完工备注",
          life: 3000,
        });
        return false;
      }
      postData.order_remark = formData.value.order_remark;
      postData.finished_remark = formData.value.finished_remark;
      break;
    case "IV": // 从新装派单到新装实施
      // 完成派单阶段，需要选择派工状态
      if (!formData.value.job_status) {
        toast.add({
          severity: "error",
          summary: "错误",
          detail: "请选择派工状态",
          life: 3000,
        });
        return false;
      }
      postData.job_status = formData.value.job_status;
      if (formData.value.order_remark) {
        postData.order_remark = formData.value.order_remark;
      }
      if (formData.value.finished_remark) {
        postData.finished_remark = formData.value.finished_remark;
      }
      break;
    case "V": // 从新装实施到服务中
      // 完成实施阶段，需要填写实施信息
      if (!formData.value.new_build_start_time) {
        toast.add({
          severity: "error",
          summary: "错误",
          detail: "请选择实施时间",
          life: 3000,
        });
        return false;
      }
      if (!formData.value.product_scheme) {
        toast.add({
          severity: "error",
          summary: "错误",
          detail: "请填写资源方案",
          life: 3000,
        });
        return false;
      }
      postData.new_build_start_time = isoFormatDate(formData.value.new_build_start_time);
      postData.product_scheme = formData.value.product_scheme;
      if (formData.value.finished_remark) {
        postData.finished_remark = formData.value.finished_remark;
      }
      break;
  }
  return true;
};

// 处理服务终止流程状态
const handleTerminationStatus = (
  newStatus: StepValue,
  postData: any
): boolean => {
  switch (newStatus) {
    case "I": // 拆机回退
      // 拆机回退不需要额外字段
      break;
    case "II": // 从拆机回退到拆机下单审核
      // 进入下单审核阶段不需要填写任何信息
      break;
    case "III": // 从拆机下单审核到拆机派单
      if (
        !formData.value.remove_required_finished_date ||
        !formData.value.order_remark ||
        !formData.value.finished_remark
      ) {
        toast.add({
          severity: "error",
          summary: "错误",
          detail: "请填写拆机要求完工日、订单备注和完工备注",
          life: 3000,
        });
        return false;
      }
      postData.remove_required_finished_date = isoFormatDate(formData.value.remove_required_finished_date);
      postData.order_remark = formData.value.order_remark;
      postData.finished_remark = formData.value.finished_remark;
      break;
    case "IV": // 从拆机派单到拆机实施
      if (
        !formData.value.order_remark ||
        !formData.value.finished_remark
      ) {
        toast.add({
          severity: "error",
          summary: "错误",
          detail: "请填写订单备注和完工备注",
          life: 3000,
        });
        return false;
      }
      postData.order_remark = formData.value.order_remark;
      postData.finished_remark = formData.value.finished_remark;
      break;
    case "V": // 从拆机实施到拆机维护信息复核
      if (
        !formData.value.remove_build_start_time ||
        !formData.value.order_remark ||
        !formData.value.finished_remark
      ) {
        toast.add({
          severity: "error",
          summary: "错误",
          detail: "请填写拆机实施时间、订单备注和完工备注",
          life: 3000,
        });
        return false;
      }
      postData.remove_build_start_time = isoFormatDatetime(formData.value.remove_build_start_time);
      postData.order_remark = formData.value.order_remark;
      postData.finished_remark = formData.value.finished_remark;
      break;
    case "VI": // 从拆机维护信息复核到服务终止
      if (
        !formData.value.order_remark ||
        !formData.value.finished_remark
      ) {
        toast.add({
          severity: "error",
          summary: "错误",
          detail: "请填写订单备注和完工备注",
          life: 3000,
        });
        return false;
      }
      postData.order_remark = formData.value.order_remark;
      postData.finished_remark = formData.value.finished_remark;
      break;
  }
  return true;
};

// 处理变更服务终止流程状态
const handleModificationStatus = (
  newStatus: StepValue,
  postData: any
): boolean => {
  switch (newStatus) {
    case "I": // 变更拆机回退
      // 变更拆机回退不需要额外字段
      break;
    case "II": // 变更拆机回退到变更拆机下单审核 无需任何字段
      break;
    case "III": // 变更拆机下单审核到变更拆机派单
      if (
        !formData.value.remove_required_finished_date ||
        !formData.value.order_remark ||
        !formData.value.finished_remark
      ) {
        toast.add({
          severity: "error",
          summary: "错误",
          detail: "请填写拆机要求完工日、订单备注和完工备注",
          life: 3000,
        });
        return false;
      }
      postData.remove_required_finished_date = isoFormatDate(formData.value.remove_required_finished_date);
      postData.order_remark = formData.value.order_remark;
      postData.finished_remark = formData.value.finished_remark;
      break;
    case "IV": // 变更拆机派单到变更拆机实施
      if (
        !formData.value.order_remark ||
        !formData.value.finished_remark
      ) {
        toast.add({
          severity: "error",
          summary: "错误",
          detail: "请填写订单备注和完工备注",
          life: 3000,
        });
        return false;
      }
      postData.order_remark = formData.value.order_remark;
      postData.finished_remark = formData.value.finished_remark;
      break;
    case "V": // 变更拆机实施到变更拆机维护信息复核
      if (
        !formData.value.remove_build_start_time ||
        !formData.value.order_remark ||
        !formData.value.finished_remark
      ) {
        toast.add({
          severity: "error",
          summary: "错误",
          detail: "请填写拆机实施时间、订单备注和完工备注",
          life: 3000,
        });
        return false;
      }
      postData.remove_build_start_time = isoFormatDatetime(formData.value.remove_build_start_time);
      postData.order_remark = formData.value.order_remark;
      postData.finished_remark = formData.value.finished_remark;
      break;
    case "VI": // 变更拆机维护信息复核到变更服务终止
      if (
        !formData.value.order_remark ||
        !formData.value.finished_remark
      ) {
        toast.add({
          severity: "error",
          summary: "错误",
          detail: "请填写订单备注和完工备注",
          life: 3000,
        });
        return false;
      }
      postData.order_remark = formData.value.order_remark;
      postData.finished_remark = formData.value.finished_remark;
      break;
  }
  return true;
};

// 检查按钮是否应该禁用
const isButtonDisabled = (
  step: StepValue,
  direction: "next" | "back"
): boolean => {
  if (!orderDetail.value) return true;

  let currentStatus: StepValue;

  if (
    (orderDetail.value.service_status as ServiceStatus) in
    installationStatusValueMap
  ) {
    currentStatus =
      installationStatusValueMap[
        orderDetail.value.service_status as InstallationStatus
      ];
  } else if (
    (orderDetail.value.service_status as ServiceStatus) in
    terminationStatusValueMap
  ) {
    currentStatus =
      terminationStatusValueMap[
        orderDetail.value.service_status as TerminationStatus
      ];
  } else if (
    (orderDetail.value.service_status as ServiceStatus) in
    modificationStatusValueMap
  ) {
    currentStatus =
      modificationStatusValueMap[
        orderDetail.value.service_status as ModificationStatus
      ];
  } else {
    return true;
  }

  // 如果当前是最终状态，禁用下一步按钮
  // 最终状态没有点击一下步的操作
  if (
    (selectedBranch.value === "installation" && currentStatus === "V") ||
    (selectedBranch.value === "termination" && currentStatus === "VI") ||
    (selectedBranch.value === "modification" && currentStatus === "VI")
  ) {
    if (direction === "next") return true;
  }

  // 服务终止和变更服务终止不可进行回退操作
  if (
    (selectedBranch.value === "termination" &&
      currentStatus === "VI" &&
      direction === "back") ||
    (selectedBranch.value === "modification" &&
      currentStatus === "VI" &&
      direction === "back")
  ) {
    return true;
  }

  // 如果不是当前步骤，禁用按钮
  if (currentStatus !== step) return true;

  if (direction === "next") {
    // 验证必填字段
    if (selectedBranch.value === "installation") {
      // 安装流程
      switch (step) {
        case "II": // 从新装下单审核到新装派单
          if (!formData.value.order_remark || !formData.value.finished_remark)
            return true;
          break;
        case "III": // 从新装派单到新装实施
          if (!formData.value.job_status) return true;
          break;
        case "IV": // 从新装实施到服务中
          if (
            !formData.value.new_build_start_time ||
            !formData.value.product_scheme
          )
            return true;
          break;
      }
    } else if (selectedBranch.value === "termination") {
      // 服务终止流程
      switch (step) {
        case "I": // 拆机回退到拆机下单审核
          break; // 没有必填字段
        case "II": // 拆机下单审核到拆机派单
          if (
            !formData.value.remove_required_finished_date ||
            !formData.value.order_remark ||
            !formData.value.finished_remark
          )
            return true;
          break;
        case "III": // 拆机派单到拆机实施
          if (!formData.value.order_remark || !formData.value.finished_remark)
            return true;
          break;
        case "IV": // 拆机实施到拆机维护信息复核
          if (
            !formData.value.remove_build_start_time ||
            !formData.value.order_remark ||
            !formData.value.finished_remark
          )
            return true;
          break;
        case "V": // 拆机维护信息复核到服务终止
          if (!formData.value.order_remark || !formData.value.finished_remark)
            return true;
          break;
      }
    } else if (selectedBranch.value === "modification") {
      // 变更服务终止流程
      switch (step) {
        case "I": // 变更拆机回退到变更拆机下单审核
          break; // 没有必填字段
        case "II": // 变更拆机下单审核到变更拆机派单
          if (
            !formData.value.remove_required_finished_date ||
            !formData.value.order_remark ||
            !formData.value.finished_remark
          )
            return true;
          break;
        case "III": // 变更拆机派单到变更拆机实施
          if (!formData.value.order_remark || !formData.value.finished_remark)
            return true;
          break;
        case "IV": // 变更拆机实施到变更拆机维护信息复核
          if (
            !formData.value.remove_build_start_time ||
            !formData.value.order_remark ||
            !formData.value.finished_remark
          )
            return true;
          break;
        case "V": // 变更拆机维护信息复核到变更服务终止
          if (!formData.value.order_remark || !formData.value.finished_remark)
            return true;
          break;
      }
    }
  }

  return false;
};

onMounted(() => {
  loadOrderDetail();
});
</script>

<template>
  <div class="process-container">
    <div class="card">
      <div class="card-header">
        <Message severity="info">订单流程管理</Message>
        <div class="order-info" v-if="orderDetail">
          <span>订单号：{{ orderDetail.total_num }}</span>
          <span class="ml-4">当前状态：{{ orderDetail.service_status }}</span>
        </div>
      </div>

      <Stepper v-model:value="activeStep" class="mt-4">
        <StepList>
          <Step
            v-for="status in currentStatuses"
            :key="status.value"
            :value="status.value"
          >
            {{ status.label }}
          </Step>
        </StepList>
        <StepPanels class="step-table">
          <!-- 安装流程 -->
          <template v-if="selectedBranch === 'installation'">
            <!-- 新装暂存 -->
            <StepPanel value="I">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：新装暂存</p>
                <div class="flex justify-end mt-4">
                  <Button
                    label="下一步"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    :disabled="isButtonDisabled('I', 'next')"
                    @click="handleStatusChange('II', 'next')"
                  />
                </div>
              </div>
            </StepPanel>

            <!-- 新装下单审核 -->
            <StepPanel value="II">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：新装下单审核</p>
                <div class="grid grid-cols-1 gap-4">
                  <div class="field">
                    <label>订单备注</label>
                    <Textarea
                      v-model="formData.order_remark"
                      rows="3"
                      autoResize
                    />
                  </div>
                  <div class="field">
                    <label>完工备注</label>
                    <Textarea
                      v-model="formData.finished_remark"
                      rows="3"
                      autoResize
                    />
                  </div>
                </div>
                <div class="flex justify-between mt-4">
                  <Button
                    label="上一步"
                    icon="pi pi-arrow-left"
                    severity="secondary"
                    :disabled="isButtonDisabled('II', 'back')"
                    @click="handleStatusChange('I', 'back')"
                  />
                  <Button
                    label="下一步"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    :disabled="isButtonDisabled('II', 'next')"
                    @click="handleStatusChange('III', 'next')"
                  />
                </div>
              </div>
            </StepPanel>

            <!-- 新装派单 -->
            <StepPanel value="III">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：新装派单</p>
                <div class="grid grid-cols-1 gap-4">
                  <div class="field">
                    <label>派工状态</label>
                    <Select
                      v-model="formData.job_status"
                      :options="job_statusOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择派工状态"
                    />
                  </div>
                  <div class="field">
                    <label>订单备注</label>
                    <Textarea
                      v-model="formData.order_remark"
                      rows="3"
                      autoResize
                    />
                  </div>
                  <div class="field">
                    <label>完工备注</label>
                    <Textarea
                      v-model="formData.finished_remark"
                      rows="3"
                      autoResize
                    />
                  </div>
                </div>
                <div class="flex justify-between mt-4">
                  <Button
                    label="上一步"
                    icon="pi pi-arrow-left"
                    severity="secondary"
                    :disabled="isButtonDisabled('III', 'back')"
                    @click="handleStatusChange('II', 'back')"
                  />
                  <Button
                    label="下一步"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    :disabled="isButtonDisabled('III', 'next')"
                    @click="handleStatusChange('IV', 'next')"
                  />
                </div>
              </div>
            </StepPanel>

            <!-- 新装实施 -->
            <StepPanel value="IV">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：新装实施</p>
                <div class="grid grid-cols-1 gap-4">
                  <div class="field">
                    <label>新装实施时间</label>
                    <DatePicker
                      v-model="formData.new_build_start_time"
                      showTime
                      dateFormat="yy-mm-dd"
                      hourFormat="24"
                      :showIcon="true"
                    />
                  </div>
                  <div class="field">
                    <label>资源开通方案</label>
                    <Textarea
                      v-model="formData.product_scheme"
                      rows="3"
                      autoResize
                    />
                  </div>
                  <div class="field">
                    <label>完工备注</label>
                    <Textarea
                      v-model="formData.finished_remark"
                      rows="3"
                      autoResize
                    />
                  </div>
                </div>
                <div class="flex justify-between mt-4">
                  <Button
                    label="上一步"
                    icon="pi pi-arrow-left"
                    severity="secondary"
                    :disabled="isButtonDisabled('IV', 'back')"
                    @click="handleStatusChange('III', 'back')"
                  />
                  <Button
                    label="下一步"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    :disabled="isButtonDisabled('IV', 'next')"
                    @click="handleStatusChange('V', 'next')"
                  />
                </div>
              </div>
            </StepPanel>

            <StepPanel value="V">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：服务中</p>
                <div class="branch-selection">
                  <p class="text-sm text-gray-600 mb-2">请选择后续流程：</p>
                  <div class="flex gap-3 mt-3">
                    <Button
                      label="服务终止"
                      icon="pi pi-times-circle"
                      severity="danger"
                      @click="goToTerminationProcess('termination')"
                    />
                    <Button
                      label="变更服务终止"
                      icon="pi pi-sync"
                      severity="warning"
                      @click="goToTerminationProcess('modification')"
                    />
                  </div>
                </div>
                <Message severity="info" class="w-full mt-4">
                  <span>订单已进入服务中状态，请选择后续流程操作</span>
                </Message>
              </div>
            </StepPanel>
          </template>

          <!-- 服务终止流程 -->
          <template v-else-if="selectedBranch === 'termination'">
            <!-- 拆机回退 -->
            <StepPanel value="I">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：拆机回退</p>
                <Message severity="info" class="w-full mb-4">
                  <span>拆机回退不需要填写任何信息，直接点击下一步</span>
                </Message>
                <div class="flex justify-end mt-4">
                  <Button
                    label="下一步"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    :disabled="isButtonDisabled('I', 'next')"
                    @click="handleStatusChange('II', 'next')"
                  />
                </div>
              </div>
            </StepPanel>

            <!-- 拆机下单审核 -->
            <StepPanel value="II">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：拆机下单审核</p>
                <div class="grid grid-cols-1 gap-4">
                  <div class="field">
                    <label
                      >拆机要求完工日 <span class="text-red-500">*</span></label
                    >
                    <DatePicker
                      v-model="formData.remove_required_finished_date"
                      :showIcon="true"
                      placeholder="选择拆机要求完工日"
                    />
                  </div>
                  <div class="field">
                    <label>订单备注 <span class="text-red-500">*</span></label>
                    <Textarea
                      v-model="formData.order_remark"
                      rows="3"
                      autoResize
                      placeholder="请填写订单备注"
                    />
                  </div>
                  <div class="field">
                    <label>完工备注 <span class="text-red-500">*</span></label>
                    <Textarea
                      v-model="formData.finished_remark"
                      rows="3"
                      autoResize
                      placeholder="请填写完工备注"
                    />
                  </div>
                </div>
                <div class="flex justify-between mt-4">
                  <Button
                    label="上一步"
                    icon="pi pi-arrow-left"
                    severity="secondary"
                    :disabled="isButtonDisabled('II', 'back')"
                    @click="handleStatusChange('I', 'back')"
                  />
                  <Button
                    label="下一步"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    :disabled="isButtonDisabled('II', 'next')"
                    @click="handleStatusChange('III', 'next')"
                  />
                </div>
              </div>
            </StepPanel>

            <!-- 拆机派单 -->
            <StepPanel value="III">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：拆机派单</p>
                <div class="grid grid-cols-1 gap-4">
                  <div class="field">
                    <label>订单备注 <span class="text-red-500">*</span></label>
                    <Textarea
                      v-model="formData.order_remark"
                      rows="3"
                      autoResize
                      placeholder="请填写订单备注"
                    />
                  </div>
                  <div class="field">
                    <label>完工备注 <span class="text-red-500">*</span></label>
                    <Textarea
                      v-model="formData.finished_remark"
                      rows="3"
                      autoResize
                      placeholder="请填写完工备注"
                    />
                  </div>
                </div>
                <div class="flex justify-between mt-4">
                  <Button
                    label="上一步"
                    icon="pi pi-arrow-left"
                    severity="secondary"
                    :disabled="isButtonDisabled('III', 'back')"
                    @click="handleStatusChange('II', 'back')"
                  />
                  <Button
                    label="下一步"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    :disabled="isButtonDisabled('III', 'next')"
                    @click="handleStatusChange('IV', 'next')"
                  />
                </div>
              </div>
            </StepPanel>

            <!-- 拆机实施 -->
            <StepPanel value="IV">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：拆机实施</p>
                <div class="grid grid-cols-1 gap-4">
                  <div class="field">
                    <label
                      >拆机实施时间 <span class="text-red-500">*</span></label
                    >
                    <DatePicker
                      v-model="formData.remove_build_start_time"
                      showTime
                      hourFormat="24"
                      :showIcon="true"
                      placeholder="选择拆机实施时间"
                    />
                  </div>
                  <div class="field">
                    <label>订单备注 <span class="text-red-500">*</span></label>
                    <Textarea
                      v-model="formData.order_remark"
                      rows="3"
                      autoResize
                      placeholder="请填写订单备注"
                    />
                  </div>
                  <div class="field">
                    <label>完工备注 <span class="text-red-500">*</span></label>
                    <Textarea
                      v-model="formData.finished_remark"
                      rows="3"
                      autoResize
                      placeholder="请填写完工备注"
                    />
                  </div>
                </div>
                <div class="flex justify-between mt-4">
                  <Button
                    label="上一步"
                    icon="pi pi-arrow-left"
                    severity="secondary"
                    :disabled="isButtonDisabled('IV', 'back')"
                    @click="handleStatusChange('III', 'back')"
                  />
                  <Button
                    label="下一步"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    :disabled="isButtonDisabled('IV', 'next')"
                    @click="handleStatusChange('V', 'next')"
                  />
                </div>
              </div>
            </StepPanel>

            <!-- 拆机维护信息复核 -->
            <StepPanel value="V">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：拆机维护信息复核</p>
                <div class="grid grid-cols-1 gap-4">
                  <div class="field">
                    <label>订单备注 <span class="text-red-500">*</span></label>
                    <Textarea
                      v-model="formData.order_remark"
                      rows="3"
                      autoResize
                      placeholder="请填写订单备注"
                    />
                  </div>
                  <div class="field">
                    <label>完工备注 <span class="text-red-500">*</span></label>
                    <Textarea
                      v-model="formData.finished_remark"
                      rows="3"
                      autoResize
                      placeholder="请填写完工备注"
                    />
                  </div>
                </div>
                <div class="flex justify-between mt-4">
                  <Button
                    label="上一步"
                    icon="pi pi-arrow-left"
                    severity="secondary"
                    :disabled="isButtonDisabled('V', 'back')"
                    @click="handleStatusChange('IV', 'back')"
                  />
                  <Button
                    label="下一步"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    :disabled="isButtonDisabled('V', 'next')"
                    @click="handleStatusChange('VI', 'next')"
                  />
                </div>
              </div>
            </StepPanel>

            <!-- 服务终止 -->
            <StepPanel value="VI">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：服务终止</p>
                <Message severity="error" class="w-full mt-4">
                  <span>订单已完成服务终止流程，不可进行回退操作</span>
                </Message>
              </div>
            </StepPanel>
          </template>

          <!-- 变更服务终止流程 -->
          <template v-else-if="selectedBranch === 'modification'">
            <!-- 变更拆机回退 -->
            <StepPanel value="I">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：变更拆机回退</p>
                <Message severity="info" class="w-full mb-4">
                  <span>变更拆机回退不需要填写任何信息，直接点击下一步</span>
                </Message>
                <div class="flex justify-end mt-4">
                  <Button
                    label="下一步"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    :disabled="isButtonDisabled('I', 'next')"
                    @click="handleStatusChange('II', 'next')"
                  />
                </div>
              </div>
            </StepPanel>

            <!-- 变更拆机下单审核 -->
            <StepPanel value="II">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：变更拆机下单审核</p>
                <div class="grid grid-cols-1 gap-4">
                  <div class="field">
                    <label
                      >拆机要求完工日 <span class="text-red-500">*</span></label
                    >
                    <DatePicker
                      v-model="formData.remove_required_finished_date"
                      :showIcon="true"
                      placeholder="选择拆机要求完工日"
                    />
                  </div>
                  <div class="field">
                    <label>订单备注 <span class="text-red-500">*</span></label>
                    <Textarea
                      v-model="formData.order_remark"
                      rows="3"
                      autoResize
                      placeholder="请填写订单备注"
                    />
                  </div>
                  <div class="field">
                    <label>完工备注 <span class="text-red-500">*</span></label>
                    <Textarea
                      v-model="formData.finished_remark"
                      rows="3"
                      autoResize
                      placeholder="请填写完工备注"
                    />
                  </div>
                </div>
                <div class="flex justify-between mt-4">
                  <Button
                    label="上一步"
                    icon="pi pi-arrow-left"
                    severity="secondary"
                    :disabled="isButtonDisabled('II', 'back')"
                    @click="handleStatusChange('I', 'back')"
                  />
                  <Button
                    label="下一步"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    :disabled="isButtonDisabled('II', 'next')"
                    @click="handleStatusChange('III', 'next')"
                  />
                </div>
              </div>
            </StepPanel>

            <!-- 变更拆机派单 -->
            <StepPanel value="III">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：变更拆机派单</p>
                <div class="grid grid-cols-1 gap-4">
                  <div class="field">
                    <label>订单备注 <span class="text-red-500">*</span></label>
                    <Textarea
                      v-model="formData.order_remark"
                      rows="3"
                      autoResize
                      placeholder="请填写订单备注"
                    />
                  </div>
                  <div class="field">
                    <label>完工备注 <span class="text-red-500">*</span></label>
                    <Textarea
                      v-model="formData.finished_remark"
                      rows="3"
                      autoResize
                      placeholder="请填写完工备注"
                    />
                  </div>
                </div>
                <div class="flex justify-between mt-4">
                  <Button
                    label="上一步"
                    icon="pi pi-arrow-left"
                    severity="secondary"
                    :disabled="isButtonDisabled('III', 'back')"
                    @click="handleStatusChange('II', 'back')"
                  />
                  <Button
                    label="下一步"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    :disabled="isButtonDisabled('III', 'next')"
                    @click="handleStatusChange('IV', 'next')"
                  />
                </div>
              </div>
            </StepPanel>

            <!-- 变更拆机实施 -->
            <StepPanel value="IV">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：变更拆机实施</p>
                <div class="grid grid-cols-1 gap-4">
                  <div class="field">
                    <label
                      >拆机实施时间 <span class="text-red-500">*</span></label
                    >
                    <DatePicker
                      v-model="formData.remove_build_start_time"
                      showTime
                      hourFormat="24"
                      :showIcon="true"
                      placeholder="选择拆机实施时间"
                    />
                  </div>
                  <div class="field">
                    <label>订单备注 <span class="text-red-500">*</span></label>
                    <Textarea
                      v-model="formData.order_remark"
                      rows="3"
                      autoResize
                      placeholder="请填写订单备注"
                    />
                  </div>
                  <div class="field">
                    <label>完工备注 <span class="text-red-500">*</span></label>
                    <Textarea
                      v-model="formData.finished_remark"
                      rows="3"
                      autoResize
                      placeholder="请填写完工备注"
                    />
                  </div>
                </div>
                <div class="flex justify-between mt-4">
                  <Button
                    label="上一步"
                    icon="pi pi-arrow-left"
                    severity="secondary"
                    :disabled="isButtonDisabled('IV', 'back')"
                    @click="handleStatusChange('III', 'back')"
                  />
                  <Button
                    label="下一步"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    :disabled="isButtonDisabled('IV', 'next')"
                    @click="handleStatusChange('V', 'next')"
                  />
                </div>
              </div>
            </StepPanel>

            <!-- 变更拆机维护信息复核 -->
            <StepPanel value="V">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：变更拆机维护信息复核</p>
                <div class="grid grid-cols-1 gap-4">
                  <div class="field">
                    <label>订单备注 <span class="text-red-500">*</span></label>
                    <Textarea
                      v-model="formData.order_remark"
                      rows="3"
                      autoResize
                      placeholder="请填写订单备注"
                    />
                  </div>
                  <div class="field">
                    <label>完工备注 <span class="text-red-500">*</span></label>
                    <Textarea
                      v-model="formData.finished_remark"
                      rows="3"
                      autoResize
                      placeholder="请填写完工备注"
                    />
                  </div>
                </div>
                <div class="flex justify-between mt-4">
                  <Button
                    label="上一步"
                    icon="pi pi-arrow-left"
                    severity="secondary"
                    :disabled="isButtonDisabled('V', 'back')"
                    @click="handleStatusChange('IV', 'back')"
                  />
                  <Button
                    label="下一步"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    :disabled="isButtonDisabled('V', 'next')"
                    @click="handleStatusChange('VI', 'next')"
                  />
                </div>
              </div>
            </StepPanel>

            <!-- 变更服务终止 -->
            <StepPanel value="VI">
              <div class="step-content">
                <p class="text-lg mb-4">当前状态：变更服务终止</p>
                <Message severity="success" class="w-full mt-4">
                  <span>订单已完成变更服务终止流程，不可进行回退操作</span>
                </Message>
              </div>
            </StepPanel>
          </template>
        </StepPanels>
        <ConfirmDialog :group="props.dialogKey" />
      </Stepper>
    </div>
  </div>
</template>
<style scoped>
.process-container {
  padding: 1rem;
  height: calc(100vh - 19rem);
  max-width: 100rem;
  margin: 0 auto;
}

.card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 1px 3px 0 rgba(0, 0, 0, 0.12);
  transition: width 0.3s ease;
}

.card-header {
  margin-bottom: 2rem;
}

.order-info {
  margin-top: 1rem;
  color: #666;
}

.step-content {
  padding: 2rem;
  background-color: var(--surface-card);
  border-radius: 8px;
}

.field {
  margin-bottom: 1.5rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.branch-selection {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  border: 1px solid var(--surface-border);
  border-radius: 6px;
  background-color: #f9f9f9;
}

:deep(.p-stepper-header) {
  border-bottom: 1px solid var(--surface-border);
  margin-bottom: 1.5rem;
}

:deep(.p-stepper-step-icon) {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: var(--surface-ground);
  color: var(--text-color);
  border: 2px solid var(--surface-border);
  transition: all 0.2s;
}

:deep(.p-stepper-step.p-highlight .p-stepper-step-icon) {
  background-color: var(--primary-color);
  color: var(--primary-color-text);
  border-color: var(--primary-color);
}

:deep(.p-stepper-step.p-disabled .p-stepper-step-icon) {
  background-color: var(--surface-ground);
  color: var(--text-color-secondary);
  border-color: var(--surface-border);
  cursor: not-allowed;
}

:deep(.p-datepicker) {
  width: 50%;
}

:deep(.p-select) {
  width: 50%;
}

:deep(.p-textarea) {
  width: 100%;
}

.step-table {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 33rem);
  overflow-y: auto;
}
</style>
